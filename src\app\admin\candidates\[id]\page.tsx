'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  FileText,
  Award,
  Download,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface TestResult {
  id: string;
  testRegistrationId: string;
  listeningBandScore: number;
  readingBandScore: number;
  writingBandScore: number;
  speakingBandScore: number;
  overallBandScore: number;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial?: string;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  registrationInfo?: {
    candidateNumber: string;
    testDate: string;
    testCenter: string;
  };
}

interface TestRegistration {
  id: string;
  candidateNumber: string;
  testDate: string;
  testCenter: string;
  status: string;
  createdAt: string;
}

interface UnifiedTestEntry {
  id: string;
  candidateNumber: string;
  testDate: string;
  testCenter: string;
  registrationDate: string;
  status: 'registered' | 'pending' | 'completed' | 'verified';
  hasResult: boolean;
  result?: TestResult;
}

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  photoUrl?: string;
  createdAt: string;
  updatedAt: string;
  testRegistrations: TestRegistration[];
  testResults: TestResult[];
}

export default function CandidateDetailsPage() {
  const params = useParams();
  const candidateId = params.id as string;

  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to combine registrations and results into unified test history
  const createUnifiedTestHistory = (registrations: TestRegistration[], results: TestResult[]): UnifiedTestEntry[] => {
    const unifiedEntries: UnifiedTestEntry[] = [];

    // Create a map of results by registration ID for quick lookup
    const resultsByRegistrationId = new Map<string, TestResult>();
    results.forEach(result => {
      if (result.registrationInfo) {
        resultsByRegistrationId.set(result.testRegistrationId, result);
      }
    });

    // Process each registration
    registrations.forEach(registration => {
      const result = resultsByRegistrationId.get(registration.id);

      unifiedEntries.push({
        id: registration.id,
        candidateNumber: registration.candidateNumber,
        testDate: registration.testDate,
        testCenter: registration.testCenter,
        registrationDate: registration.createdAt,
        status: result ? (result.status as 'pending' | 'completed' | 'verified') : 'registered',
        hasResult: !!result,
        result: result
      });
    });

    // Sort by test date (oldest first) so test numbers are chronological
    return unifiedEntries.sort((a, b) => new Date(a.testDate).getTime() - new Date(b.testDate).getTime());
  };

  const fetchCandidateDetails = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/candidates/${candidateId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch candidate details');
      }

      const data = await response.json();
      setCandidate(data);
    } catch (error) {
      console.error('Error fetching candidate details:', error);
      setError('Failed to load candidate details');
    } finally {
      setLoading(false);
    }
  }, [candidateId]);

  useEffect(() => {
    if (candidateId) {
      fetchCandidateDetails();
    }
  }, [candidateId, fetchCandidateDetails]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'verified':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'registered':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'verified':
        return <Award className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'registered':
        return <Calendar className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'verified':
        return 'Verified';
      case 'pending':
        return 'Results Pending';
      case 'registered':
        return 'Registered - Not Taken';
      default:
        return 'Unknown';
    }
  };

  const getBandScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 font-bold';
    if (score >= 6.5) return 'text-blue-600 font-semibold';
    if (score >= 5.5) return 'text-yellow-600 font-medium';
    return 'text-red-600 font-medium';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !candidate) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Link
            href="/admin/candidates"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Candidate Details</h1>
        </div>
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {error || 'Candidate not found'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/candidates"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Candidate Details</h1>
            <p className="text-gray-600">Complete candidate information and test history</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link
            href={`/admin/candidates/${candidateId}/edit`}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Edit Candidate
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Candidate Information */}
        <div className="lg:col-span-1 space-y-6">
          {/* Basic Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <User className="h-5 w-5 mr-2" />
              Personal Information
            </h2>

            <div className="flex items-start space-x-4 mb-6">
              {candidate.photoUrl ? (
                <Image
                  src={candidate.photoUrl}
                  alt={candidate.fullName}
                  width={80}
                  height={80}
                  className="rounded-lg object-cover"
                />
              ) : (
                <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                  <User className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">{candidate.fullName}</h3>
                <p className="text-gray-600">ID: {candidate.id}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center text-sm text-gray-600">
                <Mail className="h-4 w-4 mr-3" />
                <span>{candidate.email}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Phone className="h-4 w-4 mr-3" />
                <span>{candidate.phoneNumber}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-3" />
                <span>Born: {new Date(candidate.dateOfBirth).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-3" />
                <span>{candidate.nationality}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <FileText className="h-4 w-4 mr-3" />
                <span>Passport: {candidate.passportNumber}</span>
              </div>
            </div>
          </div>


        </div>

        {/* Right Column - Unified Test History */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
              <Award className="h-5 w-5 mr-2" />
              Complete Test History
            </h2>

            {(() => {
              const unifiedHistory = createUnifiedTestHistory(
                candidate.testRegistrations || [],
                candidate.testResults || []
              );

              return unifiedHistory.length > 0 ? (
                <div className="space-y-6">
                  {unifiedHistory.map((testEntry, index) => (
                    <div key={testEntry.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            Test #{index + 1} - #{testEntry.candidateNumber}
                          </h3>
                          <p className="text-sm text-gray-600">
                            Test Date: {new Date(testEntry.testDate).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            Registered: {new Date(testEntry.registrationDate).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            Test Center: {testEntry.testCenter}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(testEntry.status)}`}>
                            {getStatusIcon(testEntry.status)}
                            <span className="ml-1">{getStatusText(testEntry.status)}</span>
                          </span>
                          {testEntry.result?.certificateGenerated && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-green-800 bg-green-100">
                              <Award className="h-3 w-3 mr-1" />
                              Certificate
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Band Scores - Only show if test has results */}
                      {testEntry.hasResult && testEntry.result ? (
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                          <div className="text-center">
                            <div className="text-xs text-gray-500 mb-1">Listening</div>
                            <div className={`text-lg font-semibold ${getBandScoreColor(testEntry.result.listeningBandScore)}`}>
                              {testEntry.result.listeningBandScore}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-gray-500 mb-1">Reading</div>
                            <div className={`text-lg font-semibold ${getBandScoreColor(testEntry.result.readingBandScore)}`}>
                              {testEntry.result.readingBandScore}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-gray-500 mb-1">Writing</div>
                            <div className={`text-lg font-semibold ${getBandScoreColor(testEntry.result.writingBandScore)}`}>
                              {testEntry.result.writingBandScore}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-gray-500 mb-1">Speaking</div>
                            <div className={`text-lg font-semibold ${getBandScoreColor(testEntry.result.speakingBandScore)}`}>
                              {testEntry.result.speakingBandScore}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-gray-500 mb-1">Overall</div>
                            <div className={`text-xl font-bold ${getBandScoreColor(testEntry.result.overallBandScore)}`}>
                              {testEntry.result.overallBandScore}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-50 rounded-lg p-4 mb-4 text-center">
                          <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-600 font-medium">Test Not Taken Yet</p>
                          <p className="text-gray-500 text-sm">Results will appear here after the test is completed</p>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          {testEntry.result?.aiFeedbackGenerated && (
                            <div className="flex items-center">
                              <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                              AI Feedback Available
                            </div>
                          )}
                          {testEntry.result?.certificateSerial && (
                            <div className="flex items-center">
                              <Award className="h-4 w-4 mr-1 text-blue-500" />
                              Certificate: {testEntry.result.certificateSerial}
                            </div>
                          )}
                          {!testEntry.hasResult && (
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1 text-orange-500" />
                              Awaiting Test Date
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          {testEntry.hasResult && testEntry.result && (
                            <Link
                              href={`/results/${testEntry.result.id}`}
                              className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View Details
                            </Link>
                          )}
                          {testEntry.result?.certificateGenerated && (
                            <a
                              href={`/api/certificate/${testEntry.result.id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                            >
                              <Download className="h-3 w-3 mr-1" />
                              Certificate
                            </a>
                          )}
                          {!testEntry.hasResult && (
                            <span className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-500 rounded">
                              <Clock className="h-3 w-3 mr-1" />
                              Pending
                            </span>
                          )}
                        </div>
                      </div>
                  </div>
                ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">No test registrations found</p>
                  <p className="text-gray-400 text-sm mt-2">
                    This candidate has not registered for any IELTS tests yet
                  </p>
                </div>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
