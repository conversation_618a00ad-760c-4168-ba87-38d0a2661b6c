import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults, testRegistrations } from '@/lib/db/schema';
import { eq, desc, count, avg } from 'drizzle-orm';
import { getUpcomingSunday } from '@/lib/utils/dateHelpers';

export async function GET() {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    try {
      // Get total candidates
      const totalCandidatesResult = await db
        .select({ count: count() })
        .from(candidates);
      const totalCandidates = totalCandidatesResult[0]?.count || 0;

      // Get total results
      const totalResultsResult = await db
        .select({ count: count() })
        .from(testResults);
      const totalResults = totalResultsResult[0]?.count || 0;

      // Get pending results
      const pendingResultsResult = await db
        .select({ count: count() })
        .from(testResults)
        .where(eq(testResults.status, 'pending'));
      const pendingResults = pendingResultsResult[0]?.count || 0;

      // Get completed results
      const completedResultsResult = await db
        .select({ count: count() })
        .from(testResults)
        .where(eq(testResults.status, 'completed'));
      const completedResults = completedResultsResult[0]?.count || 0;

      // Get verified results
      const verifiedResultsResult = await db
        .select({ count: count() })
        .from(testResults)
        .where(eq(testResults.status, 'verified'));
      const verifiedResults = verifiedResultsResult[0]?.count || 0;

      // Get certificates generated count
      const certificatesGeneratedResult = await db
        .select({ count: count() })
        .from(testResults)
        .where(eq(testResults.certificateGenerated, true));
      const certificatesGenerated = certificatesGeneratedResult[0]?.count || 0;

      // Get average score
      const averageScoreResult = await db
        .select({ avg: avg(testResults.overallBandScore) })
        .from(testResults)
        .where(eq(testResults.status, 'completed'));
      const averageScore = parseFloat(averageScoreResult[0]?.avg || '0');

      // Get current test candidates (for the next upcoming Sunday)
      const nextTestDate = getUpcomingSunday();
      const currentTestCandidates = await db
        .select({
          id: candidates.id,
          fullName: candidates.fullName,
          email: candidates.email,
          candidateNumber: testRegistrations.candidateNumber,
          testDate: testRegistrations.testDate,
          createdAt: testRegistrations.createdAt,
        })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(eq(testRegistrations.testDate, nextTestDate))
        .orderBy(desc(testRegistrations.createdAt))
        .limit(10);

      // Get recent results with candidate info using new schema structure
      const recentResults = await db
        .select({
          id: testResults.id,
          overallBandScore: testResults.overallBandScore,
          status: testResults.status,
          createdAt: testResults.createdAt,
          candidate: {
            fullName: candidates.fullName,
          },
          testRegistration: {
            candidateNumber: testRegistrations.candidateNumber,
            testDate: testRegistrations.testDate,
          },
        })
        .from(testResults)
        .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .orderBy(desc(testResults.createdAt))
        .limit(10);

      return NextResponse.json({
        totalCandidates,
        totalResults,
        pendingResults,
        completedResults,
        verifiedResults,
        certificatesGenerated,
        averageScore,
        currentTestCandidates,
        recentResults,
        nextTestDate: nextTestDate.toISOString(),
      });

    } catch (dbError) {
      console.warn('Database connection failed, returning mock data:', dbError);

      // Return mock data when database is not available
      return NextResponse.json({
        totalCandidates: 0,
        totalResults: 0,
        pendingResults: 0,
        completedResults: 0,
        verifiedResults: 0,
        certificatesGenerated: 0,
        averageScore: 0,
        currentTestCandidates: [],
        recentResults: [],
        nextTestDate: getUpcomingSunday().toISOString(),
      });
    }
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
